#!/bin/bash

# Script to create the remaining essential files for all modules
# This creates the minimum viable implementation for each module

echo "Creating remaining essential files..."

# Create Work Area PostgreSQL implementations
cat > internal/modules/workarea/repo/pg/get_workarea.go << 'EOF'
package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *workAreaPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error) {
	var workArea model.WorkArea
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, code, name, created_at, updated_at, deleted_at
			FROM work_areas
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&workArea.ID,
			&workArea.Code,
			&workArea.Name,
			&workArea.CreatedAt,
			&workArea.UpdatedAt,
			&workArea.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.WorkAreaNotFoundf("work area not found", nil, nil)
			}
			return utils.InternalErrorf("failed to get work area", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &workArea, nil
}

func (w *workAreaPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		allowedProps := map[string]bool{
			"id":   true,
			"code": true,
			"name": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM work_areas
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count work areas", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (w *workAreaPostgreRepo) GetAll(ctx context.Context) ([]model.WorkArea, error) {
	var workAreas []model.WorkArea
	err := pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, code, name, created_at, updated_at, deleted_at
			FROM work_areas
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get work areas", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var workArea model.WorkArea
			err := rows.Scan(
				&workArea.ID,
				&workArea.Code,
				&workArea.Name,
				&workArea.CreatedAt,
				&workArea.UpdatedAt,
				&workArea.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan work area", err, nil)
			}
			workAreas = append(workAreas, workArea)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return workAreas, nil
}
EOF

echo "Created work area get operations"

# Create Work Area update and delete
cat > internal/modules/workarea/repo/pg/update_workarea.go << 'EOF'
package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *workAreaPostgreRepo) Update(ctx context.Context, workArea model.WorkArea) error {
	return pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE work_areas
			SET code = $2, name = $3
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			workArea.ID,
			workArea.Code,
			workArea.Name,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update work area", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.WorkAreaNotFoundf("work area not found", nil, nil)
		}

		return nil
	})
}
EOF

cat > internal/modules/workarea/repo/pg/delete_workarea.go << 'EOF'
package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (w *workAreaPostgreRepo) Delete(ctx context.Context, id string) error {
	return pg.ExecuteInSchema(ctx, w.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE work_areas
			SET deleted_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query, id)
		if err != nil {
			return utils.InternalErrorf("failed to delete work area", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.WorkAreaNotFoundf("work area not found or already deleted", nil, nil)
		}

		return nil
	})
}
EOF

echo "Created work area update and delete operations"
echo "All essential files created successfully!"
