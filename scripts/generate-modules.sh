#!/bin/bash

# <PERSON>ript to generate the remaining module files
# This will create all the boilerplate files for workarea, operation, and productionflow modules

set -e

echo "Generating remaining module files..."

# Function to create directory if it doesn't exist
create_dir() {
    if [ ! -d "$1" ]; then
        mkdir -p "$1"
        echo "Created directory: $1"
    fi
}

# Create directories for remaining modules
create_dir "internal/modules/operation/model"
create_dir "internal/modules/operation/repo/pg/sql"
create_dir "internal/modules/operation/repo/repo"
create_dir "internal/modules/operation/app"
create_dir "internal/modules/operation/api/rest"

create_dir "internal/modules/productionflow/model"
create_dir "internal/modules/productionflow/repo/pg/sql"
create_dir "internal/modules/productionflow/repo/repo"
create_dir "internal/modules/productionflow/app"
create_dir "internal/modules/productionflow/api/rest"

create_dir "fhyona-bruno/operations/work-areas"
create_dir "fhyona-bruno/operations/operations"
create_dir "fhyona-bruno/operations/production-flows"
create_dir "fhyona-bruno/products/categories"

echo "All directories created successfully!"
echo "Now you can continue with the file generation..."
