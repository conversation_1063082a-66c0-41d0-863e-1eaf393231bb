#!/bin/bash

# Test script for Brand Module
# This script tests the basic CRUD operations for the brand module

BASE_URL="http://localhost:8000"
API_BASE="$BASE_URL/api/v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓ $2${NC}"
    else
        echo -e "${RED}✗ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Check if server is running
print_info "Checking if server is running..."
if ! curl -s "$BASE_URL" > /dev/null; then
    echo -e "${RED}Server is not running on $BASE_URL${NC}"
    echo "Please start the server first"
    exit 1
fi
print_status 0 "Server is running"

# Login to get access token
print_info "Logging in to get access token..."
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}')

if [ $? -ne 0 ]; then
    echo -e "${RED}Failed to login${NC}"
    exit 1
fi

# Extract access token (assuming the response contains an access_token field)
ACCESS_TOKEN=$(echo "$LOGIN_RESPONSE" | grep -o '"access_token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$ACCESS_TOKEN" ]; then
    echo -e "${RED}Failed to extract access token${NC}"
    echo "Login response: $LOGIN_RESPONSE"
    exit 1
fi
print_status 0 "Successfully logged in"

# Test 1: Create a brand
print_info "Testing brand creation..."
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/brands" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -d '{"name":"Test Brand","code":"TEST_BRAND"}')

if [ $? -eq 0 ] && [[ "$CREATE_RESPONSE" =~ ^\"[a-zA-Z0-9_-]+\"$ ]]; then
    BRAND_ID=$(echo "$CREATE_RESPONSE" | tr -d '"')
    print_status 0 "Brand created successfully (ID: $BRAND_ID)"
else
    print_status 1 "Failed to create brand"
    echo "Response: $CREATE_RESPONSE"
    exit 1
fi

# Test 2: Get all brands
print_info "Testing get all brands..."
GET_ALL_RESPONSE=$(curl -s -X GET "$API_BASE/brands" \
    -H "Authorization: Bearer $ACCESS_TOKEN")

if [ $? -eq 0 ] && [[ "$GET_ALL_RESPONSE" =~ ^\[.*\]$ ]]; then
    print_status 0 "Successfully retrieved all brands"
else
    print_status 1 "Failed to get all brands"
    echo "Response: $GET_ALL_RESPONSE"
fi

# Test 3: Get brand by ID
print_info "Testing get brand by ID..."
GET_BY_ID_RESPONSE=$(curl -s -X GET "$API_BASE/brands/$BRAND_ID" \
    -H "Authorization: Bearer $ACCESS_TOKEN")

if [ $? -eq 0 ] && [[ "$GET_BY_ID_RESPONSE" =~ \"id\":\"$BRAND_ID\" ]]; then
    print_status 0 "Successfully retrieved brand by ID"
else
    print_status 1 "Failed to get brand by ID"
    echo "Response: $GET_BY_ID_RESPONSE"
fi

# Test 4: Update brand
print_info "Testing brand update..."
UPDATE_RESPONSE=$(curl -s -X PUT "$API_BASE/brands" \
    -H "Content-Type: application/json" \
    -H "Authorization: Bearer $ACCESS_TOKEN" \
    -d "{\"id\":\"$BRAND_ID\",\"name\":\"Updated Test Brand\",\"code\":\"UPDATED_TEST\"}")

if [ $? -eq 0 ]; then
    print_status 0 "Brand updated successfully"
else
    print_status 1 "Failed to update brand"
    echo "Response: $UPDATE_RESPONSE"
fi

# Test 5: Delete brand
print_info "Testing brand deletion..."
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/brands/$BRAND_ID" \
    -H "Authorization: Bearer $ACCESS_TOKEN")

if [ $? -eq 0 ]; then
    print_status 0 "Brand deleted successfully"
else
    print_status 1 "Failed to delete brand"
    echo "Response: $DELETE_RESPONSE"
fi

# Test 6: Verify brand is deleted (should return 404)
print_info "Verifying brand deletion..."
VERIFY_DELETE_RESPONSE=$(curl -s -w "%{http_code}" -X GET "$API_BASE/brands/$BRAND_ID" \
    -H "Authorization: Bearer $ACCESS_TOKEN")

HTTP_CODE="${VERIFY_DELETE_RESPONSE: -3}"
if [ "$HTTP_CODE" = "404" ]; then
    print_status 0 "Brand deletion verified (404 response)"
else
    print_status 1 "Brand deletion verification failed (expected 404, got $HTTP_CODE)"
fi

print_info "Brand module testing completed!"
