package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.RecipeUsecase.
func (r *recipeUsecase) Update(ctx context.Context, recipe model.RecipeUpdate) error {
	// Check if recipe exists
	existingRecipe, err := r.repo.GetByProp(ctx, "id", recipe.ID)
	if err != nil {
		return utils.InternalErrorf("Failed to get existing recipe", err, nil)
	}

	if existingRecipe == nil {
		return model.RecipeNotFoundf("Recipe not found", nil, nil)
	}

	// Check if name already exists (excluding current recipe)
	if recipe.Name != existingRecipe.Name {
		nameExists, err := r.repo.CountByProp(ctx, "name", recipe.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if recipe name exists", err, nil)
		}

		if nameExists > 0 {
			return model.RecipeConflictNamef("Recipe name already exists", nil, nil)
		}
	}

	// Check if code already exists (excluding current recipe)
	if recipe.Code != existingRecipe.Code {
		codeExists, err := r.repo.CountByProp(ctx, "code", recipe.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if recipe code exists", err, nil)
		}

		if codeExists > 0 {
			return model.RecipeConflictCodef("Recipe code already exists", nil, nil)
		}
	}

	// Update recipe
	recipeEntity := model.Recipe{
		ID:   recipe.ID,
		Name: recipe.Name,
		Code: recipe.Code,
		Type: recipe.Type,
	}

	err = r.repo.Update(ctx, recipeEntity)
	if err != nil {
		return err
	}

	// Update recipe products relationships
	err = r.repo.UpdateRecipeProducts(ctx, recipe.ID, recipe.ProductIDs)
	if err != nil {
		return utils.InternalErrorf("Failed to update recipe products relationships", err, nil)
	}

	// Update recipe components relationships
	err = r.repo.UpdateRecipeComponents(ctx, recipe.ID, recipe.Components)
	if err != nil {
		return utils.InternalErrorf("Failed to update recipe components relationships", err, nil)
	}

	return nil
}
