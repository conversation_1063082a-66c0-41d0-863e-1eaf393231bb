package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (r *recipePostgreRepo) Update(ctx context.Context, recipe model.Recipe) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE recipes
			SET name = $2, code = $3, type = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			recipe.ID,
			recipe.Name,
			recipe.Code,
			recipe.Type,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update recipe", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.RecipeNotFoundf("Recipe not found", nil, nil)
		}

		return nil
	})
}
