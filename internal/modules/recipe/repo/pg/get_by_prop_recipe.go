package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (r *recipePostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.Recipe, error) {
	var recipe model.Recipe

	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// First, get the basic recipe information
		query := fmt.Sprintf(`
			SELECT id, name, code, type, created_at, updated_at, deleted_at
			FROM recipes
			WHERE %s = $1 AND deleted_at IS NULL
		`, prop)

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&recipe.ID,
			&recipe.Name,
			&recipe.Code,
			&recipe.Type,
			&recipe.CreatedAt,
			&recipe.UpdatedAt,
			&recipe.DeletedAt,
		)

		if err != nil {
			return utils.InternalErrorf("failed to get recipe by prop", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// Get recipe products
	products, err := r.getRecipeProductsWithDetails(ctx, recipe.ID)
	if err != nil {
		return nil, err
	}
	recipe.Products = products

	// Get recipe components
	components, err := r.GetRecipeComponents(ctx, recipe.ID)
	if err != nil {
		return nil, err
	}
	recipe.Components = components

	return &recipe, nil
}
