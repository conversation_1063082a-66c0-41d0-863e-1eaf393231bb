package pg

import (
	"context"

	productModel "github.com/JosueDiazC/fhyona-v2-backend/internal/modules/product/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (r *recipePostgreRepo) GetAll(ctx context.Context) ([]model.Recipe, error) {
	var recipes []model.Recipe

	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// First, get all basic recipe information
		query := `
			SELECT id, name, code, type, created_at, updated_at, deleted_at
			FROM recipes
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get all recipes", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var recipe model.Recipe
			err := rows.Scan(
				&recipe.ID,
				&recipe.Name,
				&recipe.Code,
				&recipe.Type,
				&recipe.CreatedAt,
				&recipe.UpdatedAt,
				&recipe.DeletedAt,
			)

			if err != nil {
				return utils.InternalErrorf("failed to scan recipe", err, nil)
			}

			recipes = append(recipes, recipe)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate recipes", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// For each recipe, get products and components
	for i := range recipes {
		// Get recipe products
		products, err := r.getRecipeProductsWithDetails(ctx, recipes[i].ID)
		if err != nil {
			return nil, err
		}
		recipes[i].Products = products

		// Get recipe components
		components, err := r.GetRecipeComponents(ctx, recipes[i].ID)
		if err != nil {
			return nil, err
		}
		recipes[i].Components = components
	}

	return recipes, nil
}

func (r *recipePostgreRepo) getRecipeProductsWithDetails(ctx context.Context, recipeID string) ([]productModel.Product, error) {
	var products []productModel.Product

	err := pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT
				p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
				p.measurement_unit_id, p.brand_id, p.state,
				p.description, p.can_be_sold, p.can_be_purchased, p.cost_price, p.cost_price_total,
				p.created_at, p.updated_at, p.deleted_at,
				COALESCE(ARRAY_AGG(pc.category_id ORDER BY pc.created_at ASC) FILTER (WHERE pc.category_id IS NOT NULL), '{}') as category_ids
			FROM recipe_products rp
			JOIN products p ON rp.product_id = p.id
			LEFT JOIN product_categories pc ON p.id = pc.product_id
			WHERE rp.recipe_id = $1 AND p.deleted_at IS NULL
			GROUP BY p.id, p.name, p.image_url, p.commercial_name, p.code, p.sku_code,
					 p.measurement_unit_id, p.brand_id, p.state,
					 p.description, p.can_be_sold, p.can_be_purchased, p.cost_price, p.cost_price_total,
					 p.created_at, p.updated_at, p.deleted_at, rp.created_at
			ORDER BY rp.created_at ASC
		`

		rows, err := conn.Query(ctx, query, recipeID)
		if err != nil {
			return utils.InternalErrorf("failed to get recipe products", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var product productModel.Product
			var categoryIDs []string

			err := rows.Scan(
				&product.ID,
				&product.Name,
				&product.ImageURL,
				&product.CommercialName,
				&product.Code,
				&product.SKUCode,
				&product.MeasurementUnitID,
				&product.BrandID,
				&product.State,
				&product.Description,
				&product.CanBeSold,
				&product.CanBePurchased,
				&product.CostPrice,
				&product.CostPriceTotal,
				&product.CreatedAt,
				&product.UpdatedAt,
				&product.DeletedAt,
				&categoryIDs,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan recipe product", err, nil)
			}

			product.CategoryIDs = categoryIDs
			products = append(products, product)
		}

		if err := rows.Err(); err != nil {
			return utils.InternalErrorf("failed to iterate recipe products", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return products, nil
}
