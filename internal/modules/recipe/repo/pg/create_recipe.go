package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/recipe/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (r *recipePostgreRepo) Create(ctx context.Context, recipe model.Recipe) error {
	return pg.ExecuteInSchema(ctx, r.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO recipes (id, name, code, type)
			VALUES ($1, $2, $3, $4)
		`

		_, err := conn.Exec(ctx, query,
			recipe.ID,
			recipe.Name,
			recipe.Code,
			recipe.Type,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create recipe", err, nil)
		}

		return nil
	})
}
