package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (m *measurementUnitPostgreRepo) Update(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE measurement_units
			SET name = $2, code = $3
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			measurementUnit.ID,
			measurementUnit.Name,
			measurementUnit.Code,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update measurement unit", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.MeasurementUnitNotFoundf("measurement unit not found", nil, nil)
		}

		return nil
	})
}
