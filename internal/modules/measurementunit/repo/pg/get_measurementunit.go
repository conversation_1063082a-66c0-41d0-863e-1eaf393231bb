package pg

import (
	"context"
	"fmt"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (m *measurementUnitPostgreRepo) GetByProp(ctx context.Context, prop string, value string) (*model.MeasurementUnit, error) {
	var measurementUnit model.MeasurementUnit
	err := pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"name": true,
			"code": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM measurement_units
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(
			&measurementUnit.ID,
			&measurementUnit.Name,
			&measurementUnit.Code,
			&measurementUnit.CreatedAt,
			&measurementUnit.UpdatedAt,
			&measurementUnit.DeletedAt,
		)

		if err != nil {
			if err == pgx.ErrNoRows {
				return model.MeasurementUnitNotFoundf("measurement unit not found", err, nil)
			}
			return utils.InternalErrorf("failed to get measurement unit", err, nil)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &measurementUnit, nil
}

func (m *measurementUnitPostgreRepo) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	var count int
	err := pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		// Validate allowed properties to prevent SQL injection.
		allowedProps := map[string]bool{
			"id":   true,
			"name": true,
			"code": true,
		}

		if !allowedProps[prop] {
			return utils.BadRequestf(fmt.Sprintf("invalid property: %s", prop), nil, nil)
		}

		query := fmt.Sprintf(`
			SELECT COUNT(*)
			FROM measurement_units
			WHERE %s = $1 AND deleted_at IS NULL
		`, pgx.Identifier{prop}.Sanitize())

		row := conn.QueryRow(ctx, query, value)
		err := row.Scan(&count)

		if err != nil {
			return utils.InternalErrorf("failed to count measurement units", err, nil)
		}

		return nil
	})

	if err != nil {
		return 0, err
	}

	return count, nil
}

func (m *measurementUnitPostgreRepo) GetAll(ctx context.Context) ([]model.MeasurementUnit, error) {
	var measurementUnits []model.MeasurementUnit
	err := pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			SELECT id, name, code, created_at, updated_at, deleted_at
			FROM measurement_units
			WHERE deleted_at IS NULL
			ORDER BY created_at DESC
		`

		rows, err := conn.Query(ctx, query)
		if err != nil {
			return utils.InternalErrorf("failed to get measurement units", err, nil)
		}
		defer rows.Close()

		for rows.Next() {
			var measurementUnit model.MeasurementUnit
			err := rows.Scan(
				&measurementUnit.ID,
				&measurementUnit.Name,
				&measurementUnit.Code,
				&measurementUnit.CreatedAt,
				&measurementUnit.UpdatedAt,
				&measurementUnit.DeletedAt,
			)
			if err != nil {
				return utils.InternalErrorf("failed to scan measurement unit", err, nil)
			}
			measurementUnits = append(measurementUnits, measurementUnit)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return measurementUnits, nil
}
