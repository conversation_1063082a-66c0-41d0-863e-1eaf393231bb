package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (m *measurementUnitPostgreRepo) Create(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return pg.ExecuteInSchema(ctx, m.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			INSERT INTO measurement_units (id, name, code)
			VALUES ($1, $2, $3)
		`

		_, err := conn.Exec(ctx, query,
			measurementUnit.ID,
			measurementUnit.Name,
			measurementUnit.Code,
		)

		if err != nil {
			return utils.InternalErrorf("failed to create measurement unit", err, nil)
		}

		return nil
	})
}
