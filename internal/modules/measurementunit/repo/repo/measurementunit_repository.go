package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/repo/pg"
)

type measurementUnitRepository struct {
	pgRepo pg.MeasurementUnitPostgreRepo
}

// CountByProp implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return m.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Create(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return m.pgRepo.Create(ctx, measurementUnit)
}

// Delete implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Delete(ctx context.Context, id string) error {
	return m.pgRepo.Delete(ctx, id)
}

// GetAll implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) GetAll(ctx context.Context) ([]model.MeasurementUnit, error) {
	return m.pgRepo.GetAll(ctx)
}

// GetByProp implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) GetByProp(ctx context.Context, prop string, value string) (*model.MeasurementUnit, error) {
	return m.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.MeasurementUnitRepository.
func (m *measurementUnitRepository) Update(ctx context.Context, measurementUnit model.MeasurementUnit) error {
	return m.pgRepo.Update(ctx, measurementUnit)
}

func NewMeasurementUnitRepository(pgRepo pg.MeasurementUnitPostgreRepo) model.MeasurementUnitRepository {
	return &measurementUnitRepository{
		pgRepo: pgRepo,
	}
}
