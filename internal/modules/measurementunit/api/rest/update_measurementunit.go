package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements MeasurementUnitHandler.
func (m *measurementUnitHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[model.MeasurementUnitUpdate](w, r, m.validator)
	if err != nil {
		utils.LogErr(ctx, m.log, err)
		return
	}

	if err := m.useCase.Update(ctx, *req); err != nil {
		utils.LogErr(ctx, m.log, err)
		respErrHandler(w, r, err, "Failed to update measurement unit")
		return
	}

	rest.SuccessResponse(w, r, http.StatusNoContent)
}
