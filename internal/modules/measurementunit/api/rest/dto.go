package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
)

type measurementUnitResult struct {
	ID        string     `json:"id"`
	Name      string     `json:"name"`
	Code      string     `json:"code"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func measurementUnitToResult(measurementUnit *model.MeasurementUnit) measurementUnitResult {
	return measurementUnitResult{
		ID:        measurementUnit.ID,
		Name:      measurementUnit.Name,
		Code:      measurementUnit.Code,
		CreatedAt: measurementUnit.CreatedAt,
		UpdatedAt: measurementUnit.UpdatedAt,
		DeletedAt: measurementUnit.DeletedAt,
	}
}

type measurementUnitCreate struct {
	Name string `json:"name" validate:"required"`
	Code string `json:"code" validate:"required"`
}

func measurementUnitCreateToModel(dto measurementUnitCreate) model.MeasurementUnitCreate {
	return model.MeasurementUnitCreate{
		Name: dto.Name,
		Code: dto.Code,
	}
}
