package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/measurementunit/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

// Update implements model.MeasurementUnitUsecase.
func (m *measurementUnitUsecase) Update(ctx context.Context, measurementUnit model.MeasurementUnitUpdate) error {
	// Get the current measurement unit to check if name/code has changed
	currentMeasurementUnit, err := m.repo.GetByProp(ctx, "id", measurementUnit.ID)
	if err != nil {
		return err
	}

	// Check if name has changed and if it's already in use
	if currentMeasurementUnit.Name != measurementUnit.Name {
		nameExists, err := m.repo.CountByProp(ctx, "name", measurementUnit.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if measurement unit name exists", err, nil)
		}

		if nameExists > 0 {
			return model.MeasurementUnitConflictNamef("Measurement unit name already exists", nil, nil)
		}
	}

	// Check if code has changed and if it's already in use
	if currentMeasurementUnit.Code != measurementUnit.Code {
		codeExists, err := m.repo.CountByProp(ctx, "code", measurementUnit.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if measurement unit code exists", err, nil)
		}

		if codeExists > 0 {
			return model.MeasurementUnitConflictCodef("Measurement unit code already exists", nil, nil)
		}
	}

	// Update the measurement unit
	measurementUnitEntity := model.MeasurementUnit{
		ID:   measurementUnit.ID,
		Name: measurementUnit.Name,
		Code: measurementUnit.Code,
	}

	return m.repo.Update(ctx, measurementUnitEntity)
}
