package model

import "github.com/JosueDiazC/fhyona-v2-backend/internal/utils"

const (
	MeasurementUnitConflictCode     utils.ErrCode = utils.MeasurementUnitCode + iota
	MeasurementUnitConflictNameCode
	MeasurementUnitConflictCodeCode
	MeasurementUnitNotFoundCode
)

func MeasurementUnitConflictf(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictCode, message, err, details)
}

func MeasurementUnitConflictNamef(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictNameCode, message, err, details)
}

func MeasurementUnitConflictCodef(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitConflictCodeCode, message, err, details)
}

func MeasurementUnitNotFoundf(message string, err error, details any) utils.AppErr {
	return utils.NewError(MeasurementUnitNotFoundCode, message, err, details)
}
