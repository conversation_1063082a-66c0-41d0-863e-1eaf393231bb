package model

import "context"

type MeasurementUnitRepository interface {
	Create(ctx context.Context, measurementUnit MeasurementUnit) error
	Update(ctx context.Context, measurementUnit MeasurementUnit) error
	GetByProp(ctx context.Context, prop string, value string) (*MeasurementUnit, error)
	CountByProp(ctx context.Context, prop string, value string) (int, error)
	GetAll(ctx context.Context) ([]MeasurementUnit, error)
	Delete(ctx context.Context, id string) error
}
