package model

import "context"

type MeasurementUnitUsecase interface {
	Create(ctx context.Context, measurementUnit MeasurementUnitCreate) (string, error)
	Update(ctx context.Context, measurementUnit MeasurementUnitUpdate) error
	GetByProp(ctx context.Context, prop string, value string) (*MeasurementUnit, error)
	GetAll(ctx context.Context) ([]MeasurementUnit, error)
	Delete(ctx context.Context, id string) error
	ValidateCode(ctx context.Context, code string) error
}
