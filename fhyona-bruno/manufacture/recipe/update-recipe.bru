meta {
  name: Update Recipe
  type: http
  seq: 3
}

put {
  url: {{url}}/api/v1/recipes
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "id": "01JFQR8XQZM8YBVN2K3P4Q5R6S",
    "name": "Updated Chocolate Cake",
    "code": "CHOC_CAKE_002",
    "type": "dessert",
    "product_ids": [],
    "components": [
      {
        "product_id": "01JFQR8XQZM8YBVN2K3P4Q5R6S",
        "quantity": 3.0
      }
    ]
  }
}

tests {
  test("should return 204", function() {
    expect(res.getStatus()).to.equal(204);
  });
}
