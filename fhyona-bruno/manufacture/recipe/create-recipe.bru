meta {
  name: Create Recipe
  type: http
  seq: 2
}

post {
  url: {{url}}/api/v1/recipes
  body: json
  auth: bearer
}

auth:bearer {
  token: {{access_token}}
}

body:json {
  {
    "name": "Chocolate Cake",
    "code": "CHOC_CAKE_001",
    "type": "bulk",
    "product_ids": ["01JY10DNHX5SDNMZFKTRSHWEF6"],
    "components": [
      {
        "product_id": "01JXXK2558TKCWEJGATSJT4E35",
        "quantity": 2.5
      }
    ]
  }
}

tests {
  test("should return 201", function() {
    expect(res.getStatus()).to.equal(201);
  });
  
  test("should return recipe ID", function() {
    expect(res.getBody()).to.be.a('string');
  });
}
